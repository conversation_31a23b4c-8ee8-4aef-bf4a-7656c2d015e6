import { parentPort, workerData } from 'worker_threads'
import { OptimizedAnalysisEngine } from '../../services/analysis/OptimizedAnalysisEngine'
import type { AnalysisRequest, AnalysisResult } from '../../types/analysis'

if (!parentPort) {
  throw new Error('This script must be run as a worker thread')
}

// Initialize analysis engine with optimized settings for worker
const analysisEngine = new OptimizedAnalysisEngine()

// Enhanced error handling and performance monitoring
parentPort.on('message', async (request: AnalysisRequest) => {
  const startTime = performance.now()
  
  try {
    // Process the analysis request
    const result = await analysisEngine.analyze(request.filePath, request.options)
    
    const processingTime = performance.now() - startTime
    
    parentPort!.postMessage({
      success: true,
      result,
      requestId: request.requestId,
      processingTime,
      workerId: workerData?.workerId || 'unknown'
    })
  } catch (error) {
    const processingTime = performance.now() - startTime
    
    parentPort!.postMessage({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      requestId: request.requestId,
      processingTime,
      workerId: workerData?.workerId || 'unknown'
    })
  }
})

// Handle worker shutdown gracefully
parentPort.on('close', () => {
  // Cleanup resources if needed
  process.exit(0)
})

// Send ready signal
parentPort.postMessage({ type: 'ready', workerId: workerData?.workerId })
